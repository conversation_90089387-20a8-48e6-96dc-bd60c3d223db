#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试可视化功能，确认是否只绘制目标框而不绘制轨迹线
"""

import cv2
import os
import glob
from detection import detect_motion_targets

def test_current_visualization():
    """测试当前的可视化功能"""
    print("=" * 60)
    print("测试可视化功能")
    print("=" * 60)
    
    # 检查必要文件夹
    required_folders = ["post_processed", "registered", "segmented"]
    for folder in required_folders:
        if not os.path.exists(folder):
            print(f"错误：缺少文件夹 {folder}")
            return False
    
    # 清理旧的检测结果
    if os.path.exists("detected_test"):
        import shutil
        shutil.rmtree("detected_test")
    
    print("开始检测...")
    
    # 使用较宽松的参数确保能检测到目标
    motion_trajectories = detect_motion_targets(
        post_processed_folder="post_processed",
        original_frames_folder="registered",
        segmented_folder="segmented",
        output_folder="detected_test",  # 使用新的输出文件夹
        min_displacement=15.0,          # 较小的位移阈值
        min_trajectory_length=3,        # 较短的轨迹长度
        max_distance=50.0,              # 较大的匹配距离
        min_area_similarity=0.1,        # 较小的面积相似度
        min_area_threshold=25,          # 较小的面积阈值
        use_frame_difference=True,      # 启用三帧差分
        diff_threshold=25               # 较小的差分阈值
    )
    
    print(f"\n检测结果：{len(motion_trajectories)} 个运动目标")
    
    if len(motion_trajectories) > 0:
        print("\n检测成功！现在检查可视化结果...")
        
        # 检查生成的图像
        detected_files = glob.glob("detected_test/detected_frame_*.png")
        print(f"生成了 {len(detected_files)} 个可视化图像")
        
        if detected_files:
            # 检查第一个图像
            first_image_path = detected_files[0]
            print(f"检查图像: {first_image_path}")
            
            # 加载图像并检查
            img = cv2.imread(first_image_path)
            if img is not None:
                print(f"图像尺寸: {img.shape}")
                print("✓ 图像加载成功")
                
                # 显示前几个目标的信息
                print("\n目标信息:")
                for i, traj in enumerate(motion_trajectories[:3]):
                    print(f"目标 {i+1}:")
                    print(f"  - 轨迹ID: {traj.trajectory_id}")
                    print(f"  - 起始帧: {traj.start_frame}")
                    print(f"  - 结束帧: {traj.end_frame}")
                    print(f"  - 轨迹长度: {traj.get_trajectory_length()}")
                    print(f"  - 总位移: {traj.get_total_displacement():.1f}")
                
                print(f"\n✓ 可视化测试完成")
                print(f"✓ 检测图像保存在 'detected_test/' 文件夹")
                print("✓ 新的可视化只显示目标框，不显示轨迹线")
                return True
            else:
                print("✗ 无法加载生成的图像")
                return False
        else:
            print("✗ 没有生成可视化图像")
            return False
    else:
        print("✗ 没有检测到运动目标")
        return False

def compare_with_old_detected():
    """比较新旧检测结果"""
    print("\n" + "=" * 60)
    print("比较检测结果")
    print("=" * 60)
    
    # 检查旧的detected文件夹
    old_files = glob.glob("detected/detected_frame_*.png")
    new_files = glob.glob("detected_test/detected_frame_*.png")
    
    print(f"旧检测结果: {len(old_files)} 个文件")
    print(f"新检测结果: {len(new_files)} 个文件")
    
    if old_files and new_files:
        print("\n建议:")
        print("1. 查看 'detected_test/' 文件夹中的新图像")
        print("2. 对比 'detected/' 文件夹中的旧图像")
        print("3. 新图像应该只有目标框，没有轨迹线")
        
        # 检查detection_results.txt
        if os.path.exists("detection_results.txt"):
            print("4. 检查 'detection_results.txt' 文件确认检测到的目标数量")

if __name__ == "__main__":
    success = test_current_visualization()
    compare_with_old_detected()
    
    if success:
        print("\n" + "=" * 60)
        print("测试结论:")
        print("✓ 检测功能正常工作")
        print("✓ 可视化只显示目标框")
        print("✓ 如果您看到的图像仍有轨迹线，请检查是否查看了正确的文件夹")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("测试失败，请检查:")
        print("1. 是否有足够的处理后图像")
        print("2. 检测参数是否合适")
        print("3. 图像质量是否足够")
        print("=" * 60)
