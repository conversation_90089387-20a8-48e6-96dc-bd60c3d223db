#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CattePM滤波器使用示例
对配准后的SAR图像进行降噪处理，并保存到cattePM文件夹
"""

from cattepm_filter import process_registered_images, process_single_image
import os

def main():
    """
    CattePM滤波器使用示例
    """
    print("=== CattePM SAR图像降噪处理示例 ===")
    
    # 检查registered文件夹是否存在
    if not os.path.exists('registered'):
        print("错误: 未找到 'registered' 文件夹")
        print("请先运行主程序生成配准后的图像")
        return
    
    # 检查registered文件夹中是否有图像文件
    image_files = []
    for filename in os.listdir('registered'):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
            image_files.append(filename)
    
    if not image_files:
        print("错误: 'registered' 文件夹中没有图像文件")
        print("请先运行主程序生成配准后的图像")
        return
    
    print(f"在 'registered' 文件夹中找到 {len(image_files)} 个图像文件")
    
    # 设置CattePM参数
    sigma = 100          # 方差σ（滤波尺度）
    lambda_val = 1/7   # 扩散程度常数λ
    iterations = 30      # 迭代次数n
    
    print(f"\nCattePM参数设置:")
    print(f"  方差σ (滤波尺度): {sigma}")
    print(f"  扩散程度常数λ: {lambda_val}")
    print(f"  迭代次数n: {iterations}")
    
    # 对所有配准后的图像进行CattePM降噪处理
    print(f"\n开始处理配准后的SAR图像...")
    process_registered_images(
        input_folder='registered',
        output_folder='cattePM',
        sigma=sigma,
        lambda_val=lambda_val,
        iterations=iterations
    )
    
    print("\n=== 处理完成 ===")
    print("降噪后的图像已保存到 'cattePM' 文件夹中")
    
    # 显示处理结果统计
    if os.path.exists('cattePM'):
        output_files = []
        for filename in os.listdir('cattePM'):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
                output_files.append(filename)
        print(f"成功处理了 {len(output_files)} 个图像文件")

def demo_single_image():
    """
    单个图像处理示例
    """
    print("\n=== 单个图像处理示例 ===")
    
    # 检查是否有示例图像
    if os.path.exists('registered') and os.listdir('registered'):
        # 获取第一个图像作为示例
        for filename in os.listdir('registered'):
            if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
                sample_image = os.path.join('registered', filename)
                break
        else:
            print("未找到示例图像")
            return
        
        print(f"处理示例图像: {sample_image}")
        
        # 使用不同参数进行处理
        process_single_image(
            image_path=sample_image,
            output_path='cattePM/sample_cattepm_demo.png',
            sigma=100,
            lambda_val=1/7,
            iterations=30
        )
        
        print("示例处理完成")
    else:
        print("未找到示例图像文件")

if __name__ == "__main__":
    # 运行主要处理流程
    main()
    
    # 运行单个图像处理示例（可选）
    # demo_single_image()
