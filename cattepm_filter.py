import cv2
import numpy as np
import torch
import torch.nn.functional as F
import os
from tqdm import tqdm
import argparse

def cattepm_denoising(image, sigma=2.0, lambda_val=0.125, iterations=30):
    """
    基于CattePM模型的SAR图像降噪
    
    参数:
        image: 输入图像（配准后的SAR图像）
        sigma: 方差σ（滤波尺度），用于高斯核的标准差
        lambda_val: 扩散程度常数λ
        iterations: 迭代次数n
    
    返回:
        降噪后的图像
    """
    # 如果图像是彩色的，转换为灰度图
    is_color = len(image.shape) == 3
    if is_color:
        img_gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY).astype(np.float32)
    else:
        img_gray = image.astype(np.float32)
    
    # 创建结果图像
    I = img_gray.copy()
    
    # 确定设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 高斯核尺寸（基于sigma参数）
    ksize_gaussian = int(3 * sigma) // 2 * 2 + 1  # 确保是奇数
    
    # 预先创建卷积核
    sobel_x = torch.tensor([[[[1, 0, -1], [2, 0, -2], [1, 0, -1]]]], dtype=torch.float32, device=device)
    sobel_y = torch.tensor([[[[1, 2, 1], [0, 0, 0], [-1, -2, -1]]]], dtype=torch.float32, device=device)
    laplacian_kernel = torch.tensor([[[[0, 1, 0], [1, -4, 1], [0, 1, 0]]]], dtype=torch.float32, device=device)

    # CattePM迭代降噪
    for iter_num in range(iterations):
        # 步骤1: 应用高斯滤波 G_σ * I
        I_smoothed = cv2.GaussianBlur(I, (ksize_gaussian, ksize_gaussian), sigma)
        
        # 将NumPy数组转换为PyTorch张量
        I_tensor = torch.from_numpy(I.astype(np.float32)).to(device).unsqueeze(0).unsqueeze(0)
        I_smoothed_tensor = torch.from_numpy(I_smoothed.astype(np.float32)).to(device).unsqueeze(0).unsqueeze(0)
        
        # 步骤2: 计算平滑后图像的梯度 ∇(G_σ * I)
        gx_smoothed = F.conv2d(I_smoothed_tensor, sobel_x, padding=1)
        gy_smoothed = F.conv2d(I_smoothed_tensor, sobel_y, padding=1)
        
        # 计算梯度模值 |∇(G_σ * I)|
        grad_mag_smoothed = torch.sqrt(gx_smoothed**2 + gy_smoothed**2 + 1e-10)
        
        # 步骤3: 计算扩散系数 c(|∇G_σ * I|)
        # 使用CattePM模型的扩散函数
        K_diff = 20.0  # 扩散系数中的梯度模量参数
        c = torch.exp(-(grad_mag_smoothed**2) / (K_diff**2 + 1e-10))
        
        # 步骤4: 更新图像
        # 使用CattePM更新方程: I_t = lambda * c * Laplacian(I)
        delta_I_laplacian = F.conv2d(I_tensor, laplacian_kernel, padding=1)
        I_tensor = I_tensor + lambda_val * (c * delta_I_laplacian)
        
        # 更新I为下一次迭代准备
        I = I_tensor.squeeze().cpu().numpy()
    
    # 确保值在有效范围内
    result = np.clip(I, 0, 255).astype(np.uint8)
    
    # 如果原始图像是彩色的，将处理结果应用到每个通道
    if is_color:
        output_img = cv2.cvtColor(result, cv2.COLOR_GRAY2BGR)
        return output_img
    else:
        return result

def process_registered_images(input_folder='registered', output_folder='cattePM', 
                            sigma=2.0, lambda_val=0.125, iterations=30):
    """
    对配准后的SAR图像进行CattePM降噪处理
    
    参数:
        input_folder: 配准后图像的输入文件夹
        output_folder: 降噪后图像的输出文件夹
        sigma: 方差σ（滤波尺度）
        lambda_val: 扩散程度常数λ
        iterations: 迭代次数n
    """
    # 创建输出目录
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
        print(f"创建输出目录: {output_folder}")
    
    # 获取输入文件夹中的所有图像文件
    image_files = []
    for filename in os.listdir(input_folder):
        if filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff')):
            image_files.append(filename)
    
    # 按文件名排序
    image_files.sort()
    
    if not image_files:
        print(f"在文件夹 {input_folder} 中未找到图像文件")
        return
    
    print(f"找到 {len(image_files)} 个图像文件")
    print(f"CattePM参数: σ={sigma}, λ={lambda_val}, 迭代次数={iterations}")
    
    # 处理每个图像
    for filename in tqdm(image_files, desc="CattePM降噪处理"):
        # 读取图像
        input_path = os.path.join(input_folder, filename)
        image = cv2.imread(input_path)
        
        if image is None:
            print(f"无法读取图像: {input_path}")
            continue
        
        # 应用CattePM降噪
        denoised_image = cattepm_denoising(image, sigma=sigma, 
                                         lambda_val=lambda_val, 
                                         iterations=iterations)
        
        # 保存降噪后的图像
        output_filename = filename.replace('reg_', 'cattepm_')  # 修改文件名前缀
        output_path = os.path.join(output_folder, output_filename)
        cv2.imwrite(output_path, denoised_image)
    
    print(f"CattePM降噪处理完成！结果保存在 {output_folder} 文件夹中")

def process_single_image(image_path, output_path=None, sigma=2.0, lambda_val=0.125, iterations=30):
    """
    对单个图像进行CattePM降噪处理
    
    参数:
        image_path: 输入图像路径
        output_path: 输出图像路径，如果为None则自动生成
        sigma: 方差σ（滤波尺度）
        lambda_val: 扩散程度常数λ
        iterations: 迭代次数n
    """
    # 读取图像
    image = cv2.imread(image_path)
    if image is None:
        print(f"无法读取图像: {image_path}")
        return None
    
    print(f"处理图像: {image_path}")
    print(f"CattePM参数: σ={sigma}, λ={lambda_val}, 迭代次数={iterations}")
    
    # 应用CattePM降噪
    denoised_image = cattepm_denoising(image, sigma=sigma, 
                                     lambda_val=lambda_val, 
                                     iterations=iterations)
    
    # 生成输出路径
    if output_path is None:
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        output_dir = 'cattePM'
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        output_path = os.path.join(output_dir, f"{base_name}_cattepm.png")
    
    # 保存结果
    cv2.imwrite(output_path, denoised_image)
    print(f"降噪结果保存至: {output_path}")
    
    return denoised_image

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='CattePM SAR图像降噪处理')
    parser.add_argument('--input', type=str, default='registered', 
                        help='输入文件夹路径（包含配准后的SAR图像）')
    parser.add_argument('--output', type=str, default='cattePM', 
                        help='输出文件夹路径')
    parser.add_argument('--sigma', type=float, default=2.0, 
                        help='方差σ（滤波尺度），默认2.0')
    parser.add_argument('--lambda_val', type=float, default=0.125, 
                        help='扩散程度常数λ，默认0.125')
    parser.add_argument('--iterations', type=int, default=30, 
                        help='迭代次数n，默认30')
    parser.add_argument('--single_image', type=str, default=None,
                        help='处理单个图像的路径（可选）')
    
    args = parser.parse_args()
    
    # 检查CUDA可用性
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    if args.single_image:
        # 处理单个图像
        process_single_image(args.single_image, 
                           sigma=args.sigma, 
                           lambda_val=args.lambda_val, 
                           iterations=args.iterations)
    else:
        # 处理文件夹中的所有图像
        process_registered_images(input_folder=args.input, 
                                output_folder=args.output,
                                sigma=args.sigma, 
                                lambda_val=args.lambda_val, 
                                iterations=args.iterations)
