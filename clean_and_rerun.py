#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理旧结果并重新运行检测
"""

import os
import shutil
from detection import detect_motion_targets

def clean_old_results():
    """清理旧的检测结果"""
    print("清理旧的检测结果...")
    
    # 删除旧的detected文件夹
    if os.path.exists("detected"):
        shutil.rmtree("detected")
        print("✓ 删除旧的detected文件夹")
    
    # 删除旧的检测结果文件
    if os.path.exists("detection_results.txt"):
        os.remove("detection_results.txt")
        print("✓ 删除旧的detection_results.txt")

def run_new_detection():
    """运行新的检测"""
    print("\n开始新的动目标检测...")
    
    motion_trajectories = detect_motion_targets(
        post_processed_folder="post_processed",
        original_frames_folder="registered",
        segmented_folder="segmented",
        output_folder="detected",          # 使用原来的文件夹名
        min_displacement=15.0,             # 最小位移15像素
        min_trajectory_length=3,           # 最少3帧
        max_distance=40.0,                 # 最大匹配距离40像素
        min_area_similarity=0.2,           # 最小面积相似度0.2
        min_area_threshold=30,             # 最小面积30像素²
        use_frame_difference=True,         # 启用三帧间差分法
        diff_threshold=30                  # 帧差分阈值30
    )
    
    print(f"\n🎯 检测完成！")
    print(f"📊 检测到 {len(motion_trajectories)} 个运动目标")
    print(f"📁 可视化结果保存在 'detected/' 文件夹")
    print(f"📄 详细结果保存在 'detection_results.txt'")
    
    return motion_trajectories

if __name__ == "__main__":
    print("=" * 60)
    print("清理并重新运行动目标检测")
    print("=" * 60)
    
    # 清理旧结果
    clean_old_results()
    
    # 运行新检测
    motion_trajectories = run_new_detection()
    
    print("\n" + "=" * 60)
    print("重要提醒:")
    print("1. 新生成的图像只显示目标框，不显示轨迹线")
    print("2. 请查看 'detected/' 文件夹中的新图像")
    print("3. 如果仍看到轨迹线，请刷新文件浏览器")
    print("=" * 60)
