import cv2
import numpy as np
import matplotlib.pyplot as plt

def create_mock_sar_image_for_adaptive(size=(400, 400)):
    """
    创建一个更适合演示自适应阈值的模拟图像。
    特点：背景灰度不均匀（从左到右有渐变），目标小且对比度一般。
    """
    # 1. 创建一个从左到右的灰度渐变背景
    mock_img = np.zeros(size, dtype=np.float32)
    for i in range(size[1]):
        # 从灰度80渐变到120
        mock_img[:, i] = 80 + (i / size[1]) * 40
    
    mock_img = mock_img.astype(np.uint8)

    # 2. 在不同位置添加几个亮度不同的目标
    # 左侧目标 (背景较暗)
    cv2.rectangle(mock_img, (50, 50), (100, 100), 130, -1)
    # 右侧目标 (背景较亮)
    cv2.rectangle(mock_img, (300, 300), (350, 350), 160, -1)
    # 中间目标
    cv2.circle(mock_img, (200, 200), 25, 145, -1)

    # 3. 添加一些斑点噪声
    noise = np.random.normal(0, 10, mock_img.shape).astype(np.uint8)
    mock_img = cv2.add(mock_img, noise)
    
    cv2.imwrite("mock_sar_image_for_adaptive.png", mock_img)
    print("已创建并保存模拟SAR图像 'mock_sar_image_for_adaptive.png'")
    return "mock_sar_image_for_adaptive.png"


# --- 主程序入口 ---
if __name__ == '__main__':
    
    # 1. 准备图像
    # --- 请在这里替换为您自己的图像路径 ---
    image_path = "denoised\den_frame_0030.png"
    # image_path = create_mock_sar_image_for_adaptive()

    # 以灰度模式读取图像
    image = cv2.imread(image_path, cv2.IMREAD_GRAYSCALE)

    if image is None:
        print(f"错误：无法从路径 '{image_path}' 读取图像。")
    else:
        # --- 为了对比，我们首先展示一个失败的全局阈值方法 (Otsu) ---
        otsu_threshold, otsu_image = cv2.threshold(
            image, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU
        )
        print(f"全局Otsu算法计算出的阈值 = {otsu_threshold}")


        # 2. 【核心步骤】应用自适应阈值
        
        # --- 调整这两个参数是成功的关键 ---
        # blockSize: 邻域大小。需要根据图像中目标的大小来调整。
        # 如果目标很小，用小的blockSize；如果目标很大，用大的。必须是奇数。
        block_size = 7
        
        # C: 从均值或加权和中减去的常数。用于微调。
        # 如果背景被误识别为目标，可以增大C；如果目标被误识别为背景，可以减小C。
        constant_c = 5

        print(f"自适应阈值参数: blockSize={block_size}, C={constant_c}")

        # 方法A: 使用邻域均值
        adaptive_mean_image = cv2.adaptiveThreshold(
            image, 
            255, # 输出的最大值
            cv2.ADAPTIVE_THRESH_MEAN_C, 
            cv2.THRESH_BINARY, # 阈值类型
            block_size, 
            constant_c
        )

        # 方法B: 使用高斯加权和 (通常效果更好)
        adaptive_gaussian_image = cv2.adaptiveThreshold(
            image, 
            255, 
            cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 
            block_size, 
            constant_c
        )

        # 3. 可视化对比结果
        plt.style.use('default')
        plt.rcParams['font.sans-serif'] = ['SimHei'] # 设置中文字体
        plt.rcParams['axes.unicode_minus'] = False # 正常显示负号

        plt.figure(figsize=(14, 14))
        plt.suptitle(f'自适应阈值 vs 全局阈值 (blockSize={block_size}, C={constant_c})', fontsize=18)

        plt.subplot(2, 2, 1)
        plt.imshow(image, cmap='gray')
        plt.title('原始图像 (背景不均)')
        plt.axis('off')

        plt.subplot(2, 2, 2)
        plt.imshow(otsu_image, cmap='gray')
        plt.title(f'全局阈值 (Otsu, 阈值={otsu_threshold})\n(效果不佳，右侧目标丢失)')
        plt.axis('off')

        plt.subplot(2, 2, 3)
        plt.imshow(adaptive_mean_image, cmap='gray')
        plt.title('自适应阈值 (均值法)')
        plt.axis('off')

        plt.subplot(2, 2, 4)
        plt.imshow(adaptive_gaussian_image, cmap='gray')
        plt.title('自适应阈值 (高斯法)\n(效果最佳)')
        plt.axis('off')
        
        plt.tight_layout(rect=[0, 0, 1, 0.95])
        plt.show()