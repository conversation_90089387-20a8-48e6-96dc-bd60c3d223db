import cv2
import numpy as np
import os
import glob
from typing import List, Tu<PERSON>, Dict, Optional
from dataclasses import dataclass
import matplotlib.pyplot as plt
from connected_components import analyze_connected_components, load_processed_image


@dataclass
class ShadowRegion:
    """阴影区域数据类"""
    frame_id: int
    region_id: int
    centroid: Tuple[float, float]  # (x, y)
    area: float
    bbox: Tuple[int, int, int, int]  # (left, top, width, height)

    def distance_to(self, other: 'ShadowRegion') -> float:
        """计算与另一个阴影区域质心的欧氏距离"""
        dx = self.centroid[0] - other.centroid[0]
        dy = self.centroid[1] - other.centroid[1]
        return np.sqrt(dx*dx + dy*dy)

    def area_similarity(self, other: 'ShadowRegion') -> float:
        """计算与另一个阴影区域的面积相似度 (0-1)"""
        if self.area == 0 or other.area == 0:
            return 0.0
        ratio = min(self.area, other.area) / max(self.area, other.area)
        return ratio


@dataclass
class ShadowTrajectory:
    """阴影轨迹数据类"""
    trajectory_id: int
    regions: List[ShadowRegion]
    start_frame: int
    end_frame: int

    def add_region(self, region: ShadowRegion):
        """添加阴影区域到轨迹"""
        self.regions.append(region)
        self.end_frame = region.frame_id

    def get_total_displacement(self) -> float:
        """计算轨迹总位移"""
        if len(self.regions) < 2:
            return 0.0

        start_centroid = self.regions[0].centroid
        end_centroid = self.regions[-1].centroid
        dx = end_centroid[0] - start_centroid[0]
        dy = end_centroid[1] - start_centroid[1]
        return np.sqrt(dx*dx + dy*dy)

    def get_trajectory_length(self) -> int:
        """获取轨迹长度（帧数）"""
        return len(self.regions)

    def is_motion_target(self, min_displacement: float = 20.0, min_length: int = 3) -> bool:
        """判断是否为运动目标"""
        return (self.get_total_displacement() >= min_displacement and
                self.get_trajectory_length() >= min_length)


class ShadowTracker:
    """阴影跟踪器"""

    def __init__(self, max_distance: float = 50.0, min_area_similarity: float = 0.3,
                 min_area_threshold: int = 50):
        self.max_distance = max_distance  # 最大匹配距离
        self.min_area_similarity = min_area_similarity  # 最小面积相似度
        self.min_area_threshold = min_area_threshold  # 最小面积阈值
        self.trajectories: List[ShadowTrajectory] = []
        self.next_trajectory_id = 0
        self.active_trajectories: List[ShadowTrajectory] = []

    def extract_shadow_regions(self, frame_id: int, post_processed_image: np.ndarray) -> List[ShadowRegion]:
        """从形态学处理后的图像中提取阴影区域"""
        # 确保图像格式正确
        if len(post_processed_image.shape) == 3:
            post_processed_image = cv2.cvtColor(post_processed_image, cv2.COLOR_BGR2GRAY)

        # 反转图像用于连通区域分析（阴影变为白色）
        inverted_image = cv2.bitwise_not(post_processed_image)

        # 执行连通区域分析
        num_labels, labels_matrix, stats, centroids = analyze_connected_components(inverted_image)

        regions = []
        for i in range(1, num_labels):  # 跳过背景标签0
            area = stats[i, cv2.CC_STAT_AREA]

            # 过滤太小的区域
            if area < self.min_area_threshold:
                continue

            left = stats[i, cv2.CC_STAT_LEFT]
            top = stats[i, cv2.CC_STAT_TOP]
            width = stats[i, cv2.CC_STAT_WIDTH]
            height = stats[i, cv2.CC_STAT_HEIGHT]
            cx, cy = centroids[i]

            region = ShadowRegion(
                frame_id=frame_id,
                region_id=i,
                centroid=(cx, cy),
                area=area,
                bbox=(left, top, width, height)
            )
            regions.append(region)

        return regions

    def match_regions(self, current_regions: List[ShadowRegion]) -> None:
        """将当前帧的阴影区域与活跃轨迹进行匹配"""
        matched_trajectory_ids = set()
        matched_regions = set()

        # 为每个活跃轨迹寻找最佳匹配
        for traj in self.active_trajectories:
            if not traj.regions:
                continue

            last_region = traj.regions[-1]
            best_match = None
            best_score = float('inf')

            for i, region in enumerate(current_regions):
                if i in matched_regions:
                    continue

                # 计算距离和面积相似度
                distance = last_region.distance_to(region)
                area_sim = last_region.area_similarity(region)

                # 检查匹配条件
                if (distance <= self.max_distance and
                    area_sim >= self.min_area_similarity):

                    # 综合评分（距离越小越好，面积相似度越大越好）
                    score = distance / (area_sim + 0.1)  # 避免除零

                    if score < best_score:
                        best_score = score
                        best_match = i

            # 如果找到匹配，添加到轨迹
            if best_match is not None:
                traj.add_region(current_regions[best_match])
                matched_trajectory_ids.add(traj.trajectory_id)
                matched_regions.add(best_match)

        # 为未匹配的区域创建新轨迹
        for i, region in enumerate(current_regions):
            if i not in matched_regions:
                new_trajectory = ShadowTrajectory(
                    trajectory_id=self.next_trajectory_id,
                    regions=[region],
                    start_frame=region.frame_id,
                    end_frame=region.frame_id
                )
                self.trajectories.append(new_trajectory)
                self.active_trajectories.append(new_trajectory)
                self.next_trajectory_id += 1

        # 移除长时间未更新的轨迹（可选）
        current_frame = current_regions[0].frame_id if current_regions else 0
        self.active_trajectories = [
            traj for traj in self.active_trajectories
            if current_frame - traj.end_frame <= 5  # 最多5帧未匹配就移除
        ]

    def update(self, frame_id: int, post_processed_image: np.ndarray) -> List[ShadowRegion]:
        """更新跟踪器状态"""
        # 提取当前帧的阴影区域
        current_regions = self.extract_shadow_regions(frame_id, post_processed_image)

        # 如果有区域，进行匹配
        if current_regions:
            self.match_regions(current_regions)

        return current_regions


class MotionDetector:
    """动目标检测器"""

    def __init__(self, min_displacement: float = 20.0, min_trajectory_length: int = 3,
                 max_distance: float = 50.0, min_area_similarity: float = 0.3,
                 min_area_threshold: int = 50, use_frame_difference: bool = True,
                 diff_threshold: int = 30):
        self.min_displacement = min_displacement
        self.min_trajectory_length = min_trajectory_length
        self.tracker = ShadowTracker(max_distance, min_area_similarity, min_area_threshold)
        self.use_frame_difference = use_frame_difference
        self.diff_threshold = diff_threshold
        self.frame_buffer = []  # 存储最近的三帧用于三帧差分

    def three_frame_difference(self, frame1: np.ndarray, frame2: np.ndarray, frame3: np.ndarray) -> np.ndarray:
        """
        三帧间差分法检测运动目标

        参数:
            frame1: 第一帧（最早）
            frame2: 第二帧（中间）
            frame3: 第三帧（最新）

        返回:
            运动目标检测结果的二值图像
        """
        # 确保所有帧都是灰度图
        if len(frame1.shape) == 3:
            frame1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY)
        if len(frame2.shape) == 3:
            frame2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY)
        if len(frame3.shape) == 3:
            frame3 = cv2.cvtColor(frame3, cv2.COLOR_BGR2GRAY)

        # 计算相邻帧间的差分
        diff1 = cv2.absdiff(frame2, frame1)  # |frame2 - frame1|
        diff2 = cv2.absdiff(frame3, frame2)  # |frame3 - frame2|

        # 对差分图像进行阈值处理
        _, binary1 = cv2.threshold(diff1, self.diff_threshold, 255, cv2.THRESH_BINARY)
        _, binary2 = cv2.threshold(diff2, self.diff_threshold, 255, cv2.THRESH_BINARY)

        # 取两个差分结果的交集（AND操作）
        motion_mask = cv2.bitwise_and(binary1, binary2)

        # 形态学操作去除噪声
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        motion_mask = cv2.morphologyEx(motion_mask, cv2.MORPH_OPEN, kernel)
        motion_mask = cv2.morphologyEx(motion_mask, cv2.MORPH_CLOSE, kernel)

        return motion_mask

    def combine_detection_results(self, shadow_regions: List[ShadowRegion],
                                frame_diff_mask: np.ndarray, frame_id: int) -> List[ShadowRegion]:
        """
        结合阴影检测和帧差分检测的结果

        参数:
            shadow_regions: 阴影检测得到的区域
            frame_diff_mask: 帧差分检测得到的运动掩码
            frame_id: 当前帧ID

        返回:
            融合后的检测区域
        """
        if frame_diff_mask is None:
            return shadow_regions

        combined_regions = []

        # 对每个阴影区域，检查是否与帧差分结果重叠
        for region in shadow_regions:
            left, top, width, height = region.bbox

            # 提取阴影区域对应的帧差分掩码区域
            roi_mask = frame_diff_mask[top:top+height, left:left+width]

            # 计算重叠比例
            if roi_mask.size > 0:
                overlap_ratio = np.sum(roi_mask > 0) / roi_mask.size

                # 如果重叠比例超过阈值，则认为是运动目标
                if overlap_ratio > 0.1:  # 10%的重叠即认为是运动目标
                    combined_regions.append(region)

        # 同时从帧差分结果中提取新的运动区域
        frame_diff_regions = self.extract_regions_from_mask(frame_diff_mask, frame_id)

        # 合并结果，避免重复
        for diff_region in frame_diff_regions:
            # 检查是否与现有阴影区域重叠
            is_duplicate = False
            for shadow_region in combined_regions:
                if self.regions_overlap(diff_region, shadow_region):
                    is_duplicate = True
                    break

            if not is_duplicate:
                combined_regions.append(diff_region)

        return combined_regions

    def regions_overlap(self, region1: ShadowRegion, region2: ShadowRegion) -> bool:
        """检查两个区域是否重叠"""
        left1, top1, width1, height1 = region1.bbox
        left2, top2, width2, height2 = region2.bbox

        right1, bottom1 = left1 + width1, top1 + height1
        right2, bottom2 = left2 + width2, top2 + height2

        # 检查是否有重叠
        return not (right1 <= left2 or right2 <= left1 or bottom1 <= top2 or bottom2 <= top1)

    def extract_regions_from_mask(self, mask: np.ndarray, frame_id: int) -> List[ShadowRegion]:
        """从二值掩码中提取区域"""
        # 执行连通区域分析
        num_labels, labels_matrix, stats, centroids = analyze_connected_components(mask)

        regions = []
        for i in range(1, num_labels):  # 跳过背景标签0
            area = stats[i, cv2.CC_STAT_AREA]

            # 过滤太小的区域
            if area < self.tracker.min_area_threshold:
                continue

            left = stats[i, cv2.CC_STAT_LEFT]
            top = stats[i, cv2.CC_STAT_TOP]
            width = stats[i, cv2.CC_STAT_WIDTH]
            height = stats[i, cv2.CC_STAT_HEIGHT]
            cx, cy = centroids[i]

            region = ShadowRegion(
                frame_id=frame_id,
                region_id=i,
                centroid=(cx, cy),
                area=area,
                bbox=(left, top, width, height)
            )
            regions.append(region)

        return regions

    def detect_from_folder(self, post_processed_folder: str,
                          segmented_folder: str = "segmented") -> List[ShadowTrajectory]:
        """从post_processed文件夹中的图像序列检测动目标"""
        # 获取所有处理后的图像文件
        image_files = sorted(glob.glob(os.path.join(post_processed_folder, "post_frame_*.png")))

        # 如果使用帧差分法，还需要获取分割后的图像文件用于帧差分
        segmented_files = []
        if self.use_frame_difference:
            segmented_files = sorted(glob.glob(os.path.join(segmented_folder, "seg_frame_*.png")))
            if len(segmented_files) != len(image_files):
                print(f"警告：分割图像数量({len(segmented_files)})与后处理图像数量({len(image_files)})不匹配")
                self.use_frame_difference = False

        if not image_files:
            print(f"在文件夹 {post_processed_folder} 中未找到图像文件")
            return []

        print(f"找到 {len(image_files)} 个图像文件，开始动目标检测...")
        if self.use_frame_difference:
            print("启用三帧间差分法联合检测")

        # 逐帧处理
        for i, image_file in enumerate(image_files):
            # 加载后处理图像
            image = cv2.imread(image_file, cv2.IMREAD_GRAYSCALE)
            if image is None:
                print(f"无法加载图像: {image_file}")
                continue

            # 如果启用帧差分法且有足够的帧
            frame_diff_mask = None
            if self.use_frame_difference and i < len(segmented_files):
                # 加载分割后的图像用于帧差分
                segmented_image = cv2.imread(segmented_files[i], cv2.IMREAD_GRAYSCALE)
                if segmented_image is not None:
                    self.frame_buffer.append(segmented_image)

                    # 保持缓冲区最多3帧
                    if len(self.frame_buffer) > 3:
                        self.frame_buffer.pop(0)

                    # 当有3帧时进行三帧差分
                    if len(self.frame_buffer) == 3:
                        frame_diff_mask = self.three_frame_difference(
                            self.frame_buffer[0], self.frame_buffer[1], self.frame_buffer[2]
                        )

            # 提取阴影区域
            shadow_regions = self.tracker.extract_shadow_regions(i, image)

            # 结合阴影检测和帧差分结果
            if frame_diff_mask is not None:
                combined_regions = self.combine_detection_results(shadow_regions, frame_diff_mask, i)
            else:
                combined_regions = shadow_regions

            # 更新跟踪器 - 这是关键修复
            if combined_regions:
                self.tracker.match_regions(combined_regions)

            # 即使没有检测到区域，也要更新活跃轨迹状态
            current_frame = i
            self.tracker.active_trajectories = [
                traj for traj in self.tracker.active_trajectories
                if current_frame - traj.end_frame <= 5  # 最多5帧未匹配就移除
            ]

            if (i + 1) % 10 == 0:
                print(f"已处理 {i + 1}/{len(image_files)} 帧")

        # 筛选运动目标轨迹
        motion_trajectories = [
            traj for traj in self.tracker.trajectories
            if traj.is_motion_target(self.min_displacement, self.min_trajectory_length)
        ]

        print(f"检测到 {len(motion_trajectories)} 个运动目标轨迹")
        return motion_trajectories

    def visualize_detections(self, motion_trajectories: List[ShadowTrajectory],
                           original_frames_folder: str, output_folder: str = "detected"):
        """可视化运动目标检测结果（只绘制目标框，不绘制轨迹）"""
        if not motion_trajectories:
            print("没有运动目标需要可视化")
            return

        # 确保输出文件夹存在
        if not os.path.exists(output_folder):
            os.makedirs(output_folder)

        # 获取原始帧文件
        frame_files = sorted(glob.glob(os.path.join(original_frames_folder, "reg_frame_*.png")))
        if not frame_files:
            print(f"在文件夹 {original_frames_folder} 中未找到原始帧文件")
            return

        # 为每个目标分配颜色
        colors = [
            (0, 255, 0),    # 绿色
            (255, 0, 0),    # 蓝色
            (0, 0, 255),    # 红色
            (255, 255, 0),  # 青色
            (255, 0, 255),  # 品红色
            (0, 255, 255),  # 黄色
            (128, 0, 128),  # 紫色
            (255, 165, 0),  # 橙色
        ]

        print(f"开始可视化 {len(motion_trajectories)} 个运动目标...")

        # 为每一帧创建可视化结果
        for frame_idx, frame_file in enumerate(frame_files):
            # 加载原始帧
            frame = cv2.imread(frame_file)
            if frame is None:
                continue

            # 在帧上绘制当前帧的目标框
            for traj_idx, trajectory in enumerate(motion_trajectories):
                color = colors[traj_idx % len(colors)]

                # 只绘制当前帧的目标
                current_regions = [r for r in trajectory.regions if r.frame_id == frame_idx]
                for region in current_regions:
                    # 绘制边界框
                    left, top, width, height = region.bbox
                    cv2.rectangle(frame, (left, top), (left + width, top + height), color, 3)

                    # 绘制质心
                    cx, cy = int(region.centroid[0]), int(region.centroid[1])
                    cv2.circle(frame, (cx, cy), 6, color, -1)

                    # 添加目标ID标签
                    cv2.putText(frame, f"Target{trajectory.trajectory_id}",
                              (cx + 10, cy - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, color, 2)

                    # 添加目标信息（面积）
                    cv2.putText(frame, f"Area:{int(region.area)}",
                              (cx + 10, cy + 15), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)

            # 保存可视化结果
            output_file = os.path.join(output_folder, f"detected_frame_{frame_idx:04d}.png")
            cv2.imwrite(output_file, frame)

        print(f"可视化结果已保存到 {output_folder} 文件夹")

    def save_detection_results(self, motion_trajectories: List[ShadowTrajectory],
                             output_file: str = "detection_results.txt"):
        """保存检测结果到文本文件"""
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("SAR视频动目标检测结果\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"检测到的运动目标数量: {len(motion_trajectories)}\n\n")

            for i, trajectory in enumerate(motion_trajectories):
                f.write(f"运动目标 {i+1} (轨迹ID: {trajectory.trajectory_id}):\n")
                f.write(f"  起始帧: {trajectory.start_frame}\n")
                f.write(f"  结束帧: {trajectory.end_frame}\n")
                f.write(f"  轨迹长度: {trajectory.get_trajectory_length()} 帧\n")
                f.write(f"  总位移: {trajectory.get_total_displacement():.2f} 像素\n")
                f.write(f"  平均面积: {np.mean([r.area for r in trajectory.regions]):.2f} 像素²\n")

                # 轨迹详细信息
                f.write("  轨迹详细信息:\n")
                for region in trajectory.regions:
                    f.write(f"    帧{region.frame_id}: 质心({region.centroid[0]:.1f}, {region.centroid[1]:.1f}), "
                           f"面积{region.area:.0f}\n")
                f.write("\n")

        print(f"检测结果已保存到 {output_file}")


def detect_motion_targets(post_processed_folder: str = "post_processed",
                         original_frames_folder: str = "registered",
                         segmented_folder: str = "segmented",
                         output_folder: str = "detected",
                         min_displacement: float = 20.0,
                         min_trajectory_length: int = 3,
                         max_distance: float = 50.0,
                         min_area_similarity: float = 0.3,
                         min_area_threshold: int = 50,
                         use_frame_difference: bool = True,
                         diff_threshold: int = 30) -> List[ShadowTrajectory]:
    """
    动目标检测主函数

    参数:
        post_processed_folder: 形态学处理后图像的文件夹
        original_frames_folder: 原始帧文件夹（用于可视化）
        segmented_folder: 分割后图像文件夹（用于帧差分）
        output_folder: 输出文件夹
        min_displacement: 最小位移阈值
        min_trajectory_length: 最小轨迹长度
        max_distance: 最大匹配距离
        min_area_similarity: 最小面积相似度
        min_area_threshold: 最小面积阈值
        use_frame_difference: 是否使用三帧间差分法
        diff_threshold: 帧差分阈值

    返回:
        检测到的运动目标轨迹列表
    """
    # 创建检测器
    detector = MotionDetector(
        min_displacement=min_displacement,
        min_trajectory_length=min_trajectory_length,
        max_distance=max_distance,
        min_area_similarity=min_area_similarity,
        min_area_threshold=min_area_threshold,
        use_frame_difference=use_frame_difference,
        diff_threshold=diff_threshold
    )

    # 执行检测
    motion_trajectories = detector.detect_from_folder(post_processed_folder, segmented_folder)

    # 可视化结果（只绘制目标框）
    if motion_trajectories:
        detector.visualize_detections(motion_trajectories, original_frames_folder, output_folder)
        detector.save_detection_results(motion_trajectories)
    else:
        print("未检测到运动目标")

    return motion_trajectories


if __name__ == "__main__":
    # 示例用法
    motion_targets = detect_motion_targets(
        post_processed_folder="post_processed",
        original_frames_folder="registered",
        segmented_folder="segmented",
        output_folder="detected",
        min_displacement=50.0,  # 最小位移50像素
        min_trajectory_length=8,  # 最少8帧
        max_distance=30.0,  # 最大匹配距离30像素
        min_area_similarity=0.2,  # 最小面积相似度0.2
        min_area_threshold=50,  # 最小面积50像素²
        use_frame_difference=True,  # 启用三帧间差分法
        diff_threshold=30  # 帧差分阈值30
    )
