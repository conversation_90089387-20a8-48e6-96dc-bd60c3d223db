# SAR视频动目标检测系统 - 更新版本

## 更新内容

本次更新主要包含以下改进：

1. **只绘制目标框，不绘制轨迹**：修改了可视化函数，现在只显示检测到的目标边界框和质心，不再绘制运动轨迹线
2. **加入三帧间差分法**：集成了三帧间差分法来联合提取动目标，提高检测精度和减少误检

## 核心功能

### 1. 三帧间差分法
- 使用连续三帧图像进行差分运算
- 计算相邻帧间的绝对差值：|frame2 - frame1| 和 |frame3 - frame2|
- 对差分结果进行阈值处理并取交集
- 通过形态学操作去除噪声

### 2. 联合检测策略
- 结合阴影区域检测和帧差分检测结果
- 通过重叠度分析验证运动目标
- 从帧差分结果中提取新的运动区域
- 避免重复检测同一目标

### 3. 优化的可视化
- 只绘制目标边界框（矩形框）
- 显示目标质心（圆点）
- 添加目标ID和面积信息
- 使用不同颜色区分不同目标

## 新增参数

### 动目标检测参数
- `use_frame_difference`: 是否启用三帧间差分法（默认：True）
- `diff_threshold`: 帧差分阈值（默认：30）
- `segmented_folder`: 分割后图像文件夹路径（用于帧差分）

## 使用方法

### 1. 命令行运行（推荐）
```bash
# 使用默认参数（启用三帧差分）
python main.py --video SAR_Video_First.mp4

# 自定义参数
python main.py --video SAR_Video_First.mp4 \
    --use_frame_difference True \
    --diff_threshold 25 \
    --min_displacement 30.0 \
    --min_trajectory_length 5
```

### 2. 直接调用检测函数
```python
from detection import detect_motion_targets

motion_targets = detect_motion_targets(
    post_processed_folder="post_processed",
    original_frames_folder="registered", 
    segmented_folder="segmented",
    output_folder="detected",
    use_frame_difference=True,
    diff_threshold=30
)
```

### 3. 测试脚本
```bash
python test_detection.py
```

## 算法流程

1. **图像预处理**：配准、降噪、分割、形态学处理
2. **阴影区域提取**：从后处理图像中提取连通区域
3. **三帧差分**：使用分割后的图像进行三帧间差分
4. **结果融合**：结合阴影检测和帧差分结果
5. **目标跟踪**：基于质心距离和面积相似度进行帧间匹配
6. **运动判定**：根据位移和轨迹长度筛选运动目标
7. **结果可视化**：绘制目标框和相关信息

## 输出结果

### 可视化图像
- 保存在 `detected/` 文件夹中
- 文件名格式：`detected_frame_XXXX.png`
- 每个目标用不同颜色的边界框标识
- 显示目标ID和面积信息

### 检测结果文件
- `detection_results.txt`：包含详细的检测统计信息
- 每个运动目标的轨迹信息、位移、面积等数据

## 参数调优建议

### 三帧差分参数
- `diff_threshold`：
  - 较小值（20-25）：对微小运动更敏感，但可能增加噪声
  - 较大值（35-40）：减少噪声，但可能漏检慢速目标

### 检测参数
- `min_displacement`：根据目标运动速度调整
- `min_trajectory_length`：根据视频帧率和目标持续时间调整
- `max_distance`：根据目标运动速度和帧率调整

## 性能优化

1. **内存管理**：三帧缓冲区自动管理，避免内存溢出
2. **计算效率**：只在有足够帧时进行三帧差分
3. **结果融合**：智能合并检测结果，避免重复计算

## 注意事项

1. 确保 `segmented/` 文件夹中有对应的分割图像文件
2. 三帧差分需要至少3帧图像才能开始工作
3. 帧差分阈值需要根据具体场景调整
4. 建议先用默认参数测试，再根据结果调优

## 文件结构

```
detection.py              # 更新的动目标检测实现
main.py                  # 更新的主处理流程
test_detection.py        # 更新的测试脚本
README_动目标检测_更新.md  # 本说明文件
detected/                # 可视化结果（只有目标框）
detection_results.txt    # 检测结果统计
```
