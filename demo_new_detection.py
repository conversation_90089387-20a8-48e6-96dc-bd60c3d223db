#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SAR视频动目标检测演示脚本 - 新版本
展示三帧间差分法和只绘制目标框的功能
"""

import os
import sys
from detection import detect_motion_targets

def demo_detection():
    """演示新的动目标检测功能"""
    
    print("=" * 60)
    print("SAR视频动目标检测演示 - 新版本")
    print("=" * 60)
    
    # 检查必要的文件夹是否存在
    required_folders = ["post_processed", "registered", "segmented"]
    missing_folders = []
    
    for folder in required_folders:
        if not os.path.exists(folder):
            missing_folders.append(folder)
    
    if missing_folders:
        print(f"错误：缺少必要的文件夹: {missing_folders}")
        print("请先运行主处理流程生成这些文件夹")
        return
    
    print("✓ 检查文件夹完成")
    
    # 检查文件数量
    import glob
    post_files = glob.glob("post_processed/post_frame_*.png")
    reg_files = glob.glob("registered/reg_frame_*.png") 
    seg_files = glob.glob("segmented/seg_frame_*.png")
    
    print(f"✓ 找到 {len(post_files)} 个后处理图像")
    print(f"✓ 找到 {len(reg_files)} 个配准图像")
    print(f"✓ 找到 {len(seg_files)} 个分割图像")
    
    if len(post_files) == 0:
        print("错误：没有找到处理后的图像文件")
        return
    
    print("\n开始动目标检测...")
    print("新功能特点：")
    print("1. 使用三帧间差分法联合检测")
    print("2. 只绘制目标框，不绘制轨迹")
    print("3. 显示目标ID和面积信息")
    
    # 执行检测 - 使用新参数
    try:
        motion_trajectories = detect_motion_targets(
            post_processed_folder="post_processed",
            original_frames_folder="registered",
            segmented_folder="segmented",  # 新增：分割图像文件夹
            output_folder="detected_new",   # 使用新的输出文件夹
            min_displacement=20.0,          # 最小位移20像素
            min_trajectory_length=3,        # 最少3帧
            max_distance=40.0,              # 最大匹配距离40像素
            min_area_similarity=0.2,        # 最小面积相似度0.2
            min_area_threshold=30,          # 最小面积30像素²
            use_frame_difference=True,      # 新增：启用三帧间差分法
            diff_threshold=25               # 新增：帧差分阈值25
        )
        
        print(f"\n检测完成！")
        print(f"检测到 {len(motion_trajectories)} 个运动目标")
        
        if motion_trajectories:
            print("\n检测结果摘要：")
            for i, traj in enumerate(motion_trajectories):
                print(f"目标 {i+1}:")
                print(f"  - 轨迹ID: {traj.trajectory_id}")
                print(f"  - 持续帧数: {traj.get_trajectory_length()}")
                print(f"  - 总位移: {traj.get_total_displacement():.1f} 像素")
                print(f"  - 起始帧: {traj.start_frame}")
                print(f"  - 结束帧: {traj.end_frame}")
        
        print(f"\n可视化结果已保存到 'detected_new/' 文件夹")
        print("特点：只显示目标边界框和质心，不显示轨迹线")
        
    except Exception as e:
        print(f"检测过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def compare_with_old_version():
    """与旧版本进行对比"""
    print("\n" + "=" * 60)
    print("新旧版本对比")
    print("=" * 60)
    
    print("旧版本特点：")
    print("- 只使用阴影区域检测")
    print("- 绘制完整的运动轨迹线")
    print("- 可能存在误检和漏检")
    
    print("\n新版本改进：")
    print("- 结合三帧间差分法，提高检测精度")
    print("- 只绘制目标框，界面更清晰")
    print("- 显示目标面积等详细信息")
    print("- 可配置帧差分阈值")
    
    print("\n推荐使用场景：")
    print("- 目标运动较快：使用较小的diff_threshold (20-25)")
    print("- 目标运动较慢：使用较大的diff_threshold (30-35)")
    print("- 噪声较多：增大min_area_threshold")
    print("- 需要检测小目标：减小min_area_threshold")

if __name__ == "__main__":
    demo_detection()
    compare_with_old_version()
    
    print("\n" + "=" * 60)
    print("演示完成！")
    print("如需调整参数，请修改 demo_new_detection.py 中的参数值")
    print("或使用命令行运行: python main.py --help 查看所有参数")
    print("=" * 60)
